from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, select, cast as sql_cast, Numeric, and_, case
from datetime import datetime
from typing import List, Optional
from decimal import Decimal
import logging

from database.session import get_db
from models.sale import Sale, SaleItem
from models.product import Product
from schemas.dashboard import DashboardStats, SalesTrend
from utils.auth import get_current_user
from models.user import User
from services.cache_service import cache_service, cached
from utils.datetime_utils import get_tripoli_now, convert_to_tripoli_time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])

def cleanup_old_backups(backup_dir: str, max_backups: int = 10):
    """
    تنظيف النسخ الاحتياطية القديمة والاحتفاظ بعدد محدد فقط
    """
    try:
        import os

        if not os.path.exists(backup_dir):
            return

        # البحث عن جميع ملفات النسخ الاحتياطية
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.endswith('.db'):
                file_path = os.path.join(backup_dir, filename)
                # الحصول على تاريخ التعديل
                mtime = os.path.getmtime(file_path)
                backup_files.append((filename, file_path, mtime))

        # ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
        backup_files.sort(key=lambda x: x[2], reverse=True)

        # حذف الملفات الزائدة عن الحد المسموح
        if len(backup_files) > max_backups:
            files_to_delete = backup_files[max_backups:]
            for filename, file_path, _ in files_to_delete:
                try:
                    os.remove(file_path)
                    logger.info(f"تم حذف النسخة الاحتياطية القديمة: {filename}")
                except Exception as e:
                    logger.error(f"فشل في حذف النسخة الاحتياطية {filename}: {str(e)}")

        logger.info(f"تم تنظيف النسخ الاحتياطية. الملفات المتبقية: {min(len(backup_files), max_backups)}")

    except Exception as e:
        logger.error(f"خطأ في تنظيف النسخ الاحتياطية: {str(e)}")

@cached("dashboard_stats", ttl=30)  # تخزين مؤقت لمدة 30 ثانية
def _get_cached_dashboard_stats(user_id: int, user_role: str, db: Session):
    """دالة مساعدة للحصول على إحصائيات لوحة التحكم مع تخزين مؤقت"""
    logger.info(f"Fetching dashboard stats from database for user {user_id}")

    # نفس منطق الاستعلام الأصلي
    if user_role == "ADMIN":
        logger.info(f"Admin user: showing all sales from all users")
        # استعلام إجمالي المبيعات والإيرادات
        total_sales_stmt = select(
            func.count(Sale.id).label("total_sales"),
            sql_cast(func.coalesce(func.sum(Sale.amount_paid), 0), Numeric).label("total_revenue")
        )
        total_result = db.execute(total_sales_stmt).first()
        total_sales = int(total_result.total_sales) if total_result else 0
        total_revenue = float(Decimal(str(total_result.total_revenue))) if total_result else 0.0
    else:
        # للمستخدمين العاديين - عرض مبيعاتهم فقط
        total_sales_stmt = select(
            func.count(Sale.id).label("total_sales"),
            sql_cast(func.coalesce(func.sum(Sale.amount_paid), 0), Numeric).label("total_revenue")
        ).where(Sale.user_id == user_id)
        total_result = db.execute(total_sales_stmt).first()
        total_sales = int(total_result.total_sales) if total_result else 0
        total_revenue = float(Decimal(str(total_result.total_revenue))) if total_result else 0.0

    return {
        "total_sales": total_sales,
        "total_revenue": total_revenue
    }

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard statistics."""
    try:
        # استخدام البيانات المخزنة مؤقتاً للجزء الثقيل
        cached_data = _get_cached_dashboard_stats(current_user.id, current_user.role.name, db)

        logger.info("Fetching dashboard stats")

        # استخدام البيانات المخزنة مؤقتاً
        total_sales = cached_data["total_sales"]
        total_revenue = cached_data["total_revenue"]

        logger.info(f"User {current_user.username} has role: {current_user.role}")
        logger.info(f"Found {total_sales} total sales with total revenue: {total_revenue}")

        # Calculate today's sales and revenue based on user role
        # استخدام توقيت طرابلس للحصول على التاريخ الصحيح
        from utils.datetime_utils import get_tripoli_now

        tripoli_now = get_tripoli_now()
        today = tripoli_now.date()
        today_str = today.strftime('%Y-%m-%d')

        # Log the current date for debugging
        logger.info(f"Current date (Tripoli time): {today}, formatted as: {today_str}")

        # Create date filter condition using PostgreSQL date function
        # This is more reliable in PostgreSQL than using LIKE with timestamps
        date_condition = func.date(Sale.created_at) == today_str

        # Log the SQL condition for debugging
        logger.info(f"Today's date condition: date(created_at) = '{today_str}'")

        # For admin, show all sales for today
        # For regular users, show only their own sales for today
        # Use the same role check as above
        # Calculate actual paid amount for today: amount_paid (only what was actually received)
        if current_user.role.name == "ADMIN":
            logger.info(f"Admin user: showing all sales for today from all users")
            today_sales_stmt = select(
                func.count(Sale.id).label("today_sales"),
                sql_cast(func.coalesce(
                    func.sum(Sale.amount_paid),
                    0
                ), Numeric).label("today_revenue")
            ).where(date_condition)
        else:
            logger.info(f"Regular user: showing only user's sales for today")
            # Create a new condition list with both date and user conditions
            today_sales_stmt = select(
                func.count(Sale.id).label("today_sales"),
                sql_cast(func.coalesce(
                    func.sum(Sale.amount_paid),
                    0
                ), Numeric).label("today_revenue")
            ).where(
                and_(
                    date_condition,
                    Sale.user_id == current_user.id
                )
            )

        # Execute the query and log the SQL
        logger.info(f"Executing today's sales query: {str(today_sales_stmt)}")
        today_sales_result = db.execute(today_sales_stmt).first()

        if not today_sales_result:
            logger.warning(f"No sales data found for today for user {current_user.username}")
            today_sales = 0
            today_revenue = 0.0
        else:
            today_sales = int(today_sales_result[0])
            today_revenue = float(Decimal(str(today_sales_result[1])))
            logger.info(f"Found {today_sales} sales for today with total revenue: {today_revenue}")

        logger.info(f"Total sales (all time): {total_sales}, Total revenue (all time): {total_revenue}")
        logger.info(f"Today's sales for {current_user.username}: {today_sales}, Today's revenue: {today_revenue}")

        # Calculate today's profits - Simple and Logical Calculation (WITHOUT TAX)
        # الحساب البسيط والمنطقي للأرباح بدون احتساب الضرائب:
        # الربح = (سعر البيع - سعر التكلفة) × الكمية × نسبة المبلغ المدفوع
        if current_user.role.name == "ADMIN":
            logger.info(f"Admin user: calculating today's profits from all users")
            today_profits_stmt = select(
                sql_cast(func.coalesce(func.sum(
                    # الربح البسيط = (سعر البيع - سعر التكلفة) × الكمية × نسبة الدفع (محدودة بـ 100%)
                    SaleItem.quantity * (SaleItem.unit_price - Product.cost_price) *
                    case(
                        # نسبة المبلغ المدفوع من إجمالي الفاتورة (بدون الضرائب) محدودة بـ 100%
                        ((Sale.total_amount - func.coalesce(Sale.discount_amount, 0)) > 0,
                         func.least(sql_cast(1.0, Numeric), sql_cast(Sale.amount_paid, Numeric) / func.nullif(sql_cast(Sale.total_amount - func.coalesce(Sale.discount_amount, 0), Numeric), 0))
                        ),
                        else_=0
                    )
                ), 0), Numeric).label("today_profits")
            ).select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .join(Product, SaleItem.product_id == Product.id)\
                .where(date_condition)
        else:
            logger.info(f"Regular user: calculating only user's today's profits")
            today_profits_stmt = select(
                sql_cast(func.coalesce(func.sum(
                    # الربح البسيط = (سعر البيع - سعر التكلفة) × الكمية × نسبة الدفع (محدودة بـ 100%)
                    SaleItem.quantity * (SaleItem.unit_price - Product.cost_price) *
                    case(
                        # نسبة المبلغ المدفوع من إجمالي الفاتورة (بدون الضرائب) محدودة بـ 100%
                        ((Sale.total_amount - func.coalesce(Sale.discount_amount, 0)) > 0,
                         func.least(sql_cast(1.0, Numeric), sql_cast(Sale.amount_paid, Numeric) / func.nullif(sql_cast(Sale.total_amount - func.coalesce(Sale.discount_amount, 0), Numeric), 0))
                        ),
                        else_=0
                    )
                ), 0), Numeric).label("today_profits")
            ).select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .join(Product, SaleItem.product_id == Product.id)\
                .where(and_(date_condition, Sale.user_id == current_user.id))

        today_profits_result = db.execute(today_profits_stmt).first()
        today_profits = float(Decimal(str(today_profits_result[0]))) if today_profits_result else 0.0

        logger.info(f"Today's profits for {current_user.username}: {today_profits}")

        # Get product counts
        product_count_stmt = select(func.count(Product.id))
        product_count = int(db.execute(product_count_stmt).scalar() or 0)

        low_stock_stmt = select(func.count(Product.id))\
            .where(
                Product.quantity <= Product.min_quantity,
                Product.quantity > 0
            )
        low_stock_count = int(db.execute(low_stock_stmt).scalar() or 0)

        # Get today's debt statistics based on user role
        # Calculate debts created today from sales made today
        from models.customer import CustomerDebt
        from models.sale import Sale as SaleModel

        if current_user.role.name == "ADMIN":
            # Admin sees today's debts from all users
            logger.info("Admin user: showing today's debts from all users")
            # حساب ديون اليوم: (total_amount - discount + tax) - amount_paid للمبيعات التي تمت اليوم
            today_debts_query = select(
                func.coalesce(func.sum(
                    (Sale.total_amount - func.coalesce(Sale.discount_amount, 0) + func.coalesce(Sale.tax_amount, 0)) - Sale.amount_paid
                ), 0)
            ).where(date_condition)

            unpaid_debts_query = select(func.coalesce(func.sum(CustomerDebt.remaining_amount), 0)).where(
                CustomerDebt.is_paid == False
            )
        else:
            # Regular users see only today's debts they created
            logger.info("Regular user: showing only user's today's debts")
            # حساب ديون اليوم للمستخدم: (total_amount - discount + tax) - amount_paid للمبيعات التي قام بها اليوم
            today_debts_query = select(
                func.coalesce(func.sum(
                    (Sale.total_amount - func.coalesce(Sale.discount_amount, 0) + func.coalesce(Sale.tax_amount, 0)) - Sale.amount_paid
                ), 0)
            ).where(and_(date_condition, Sale.user_id == current_user.id))

            unpaid_debts_query = select(func.coalesce(func.sum(CustomerDebt.remaining_amount), 0)).join(
                SaleModel, CustomerDebt.sale_id == SaleModel.id
            ).where(
                and_(SaleModel.user_id == current_user.id, CustomerDebt.is_paid == False)
            )

        today_debts = float(db.execute(today_debts_query).scalar() or 0)
        unpaid_debts = float(db.execute(unpaid_debts_query).scalar() or 0)

        logger.info(f"Product count: {product_count}, Low stock count: {low_stock_count}")
        logger.info(f"Today's debts: {today_debts}, Unpaid debts: {unpaid_debts}")

        # Get recent sales based on user role
        # For admin, show recent sales from all users
        # For regular users, show only their own recent sales
        if current_user.role.name == "ADMIN":
            logger.info(f"Admin user: showing recent sales from all users")
            recent_sales_stmt = select(Sale)\
                .order_by(Sale.created_at.desc())\
                .limit(5)
        else:
            logger.info(f"Regular user: showing only user's recent sales")
            recent_sales_stmt = select(Sale)\
                .where(Sale.user_id == current_user.id)\
                .order_by(Sale.created_at.desc())\
                .limit(5)

        recent_sales = db.execute(recent_sales_stmt).scalars().all()

        # Get top products with correct revenue calculation
        # نحتاج لحساب نصيب كل منتج من الخصم والضريبة بناءً على نسبته من إجمالي الفاتورة
        top_products_stmt = select(
            Product.id,
            Product.name,
            sql_cast(func.coalesce(func.sum(SaleItem.quantity), 0), Numeric).label("quantity"),
            sql_cast(func.coalesce(func.sum(
                # حساب نصيب المنتج من المبلغ النهائي
                # نصيب المنتج = (سعر المنتج / إجمالي الفاتورة) × (إجمالي الفاتورة - الخصم + الضريبة)
                SaleItem.subtotal * (
                    (Sale.total_amount - func.coalesce(Sale.discount_amount, 0) + func.coalesce(Sale.tax_amount, 0)) /
                    func.nullif(Sale.total_amount, 0)
                )
            ), 0), Numeric).label("total")
        ).join(SaleItem, isouter=True)\
            .join(Sale, SaleItem.sale_id == Sale.id, isouter=True)\
            .group_by(Product.id, Product.name)\
            .order_by(func.sum(
                SaleItem.subtotal * (
                    (Sale.total_amount - func.coalesce(Sale.discount_amount, 0) + func.coalesce(Sale.tax_amount, 0)) /
                    func.nullif(Sale.total_amount, 0)
                )
            ).desc().nulls_last())\
            .limit(5)
        top_products = db.execute(top_products_stmt).all()

        response_data = {
            "totalSales": total_sales,
            "totalRevenue": total_revenue,
            "todaySales": today_revenue,  # Use today's revenue for the dashboard card
            "todayProfits": today_profits,  # Today's profits
            "totalDebts": today_debts,  # Use today's debts instead of total debts
            "unpaidDebts": unpaid_debts,
            "lowStockCount": low_stock_count,
            "recentSales": [{
                "id": sale.id,
                "total": float(Decimal(str(sale.amount_paid or 0))),  # Show only paid amount
                "createdAt": sale.created_at.isoformat(),
                "items": len(sale.items),
                "payment_method": sale.payment_method,
                "amount_paid": float(Decimal(str(sale.amount_paid or 0))),
                "total_amount": float(Decimal(str(sale.total_amount + (sale.tax_amount or 0) - (sale.discount_amount or 0)))),
                "payment_status": sale.payment_status
            } for sale in recent_sales],
            "topProducts": [{
                "id": row.id,
                "name": row.name,
                "quantity": int(Decimal(str(row.quantity))),
                "total": float(Decimal(str(row.total)))
            } for row in top_products]
        }

        logger.info("Successfully fetched dashboard stats")
        return response_data

    except Exception as e:
        logger.error(f"Error fetching dashboard stats: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching dashboard stats: {str(e)}"
        )

@router.get("/sales-trends/{period}", response_model=List[SalesTrend])
async def get_sales_trends(
    period: str,
    previous: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get sales trends for a specific period.
    Periods: day, week, month, year
    If previous=True, returns data for the previous equivalent period.
    """
    try:
        period_type = "previous" if previous else "current"
        logger.info(f"Fetching {period_type} sales trends for period: {period}")

        # استخدام الخدمات المنفصلة لجلب البيانات
        if previous:
            # استخدام خدمة الفترة السابقة
            from services.previous_period_service import PreviousPeriodService
            service = PreviousPeriodService(db, current_user)
            sales_data = service.get_sales_by_period(period)
        else:
            # استخدام خدمة الفترة الحالية
            from services.current_period_service import CurrentPeriodService
            service = CurrentPeriodService(db, current_user)
            sales_data = service.get_sales_by_period(period)

        logger.info(f"Successfully fetched {len(sales_data)} {period_type} sales trends for period: {period}")
        return sales_data

    except Exception as e:
        logger.error(f"Error fetching sales trends: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/profits/{period}")
async def get_profits_by_period(
    period: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    جلب الأرباح للفترة المحددة (day, week, month, year)
    """
    try:
        logger.info(f"جلب أرباح الفترة {period} للمستخدم {current_user.username}")

        # التحقق من صحة الفترة
        if period not in ['day', 'week', 'month', 'year']:
            raise HTTPException(status_code=400, detail="Invalid period. Must be one of: day, week, month, year")

        # حساب الأرباح للفترة المحددة
        from utils.datetime_utils import get_tripoli_now
        from datetime import timedelta

        tripoli_now = get_tripoli_now()

        # تحديد نطاق التاريخ حسب الفترة بشكل دقيق
        # تهيئة المتغير بقيمة افتراضية
        date_condition = None

        if period == 'day':
            # أرباح اليوم الحالي فقط
            today = tripoli_now.date()
            date_condition = func.date(Sale.created_at) == today.strftime('%Y-%m-%d')
            logger.info(f"حساب أرباح اليوم: {today}")

        elif period == 'week':
            # أرباح آخر 7 أيام (بما في ذلك اليوم الحالي)
            week_start = (tripoli_now - timedelta(days=6)).date()  # 6 أيام مضت + اليوم = 7 أيام
            week_end = tripoli_now.date()
            date_condition = and_(
                func.date(Sale.created_at) >= week_start.strftime('%Y-%m-%d'),
                func.date(Sale.created_at) <= week_end.strftime('%Y-%m-%d')
            )
            logger.info(f"حساب أرباح الأسبوع: من {week_start} إلى {week_end}")

        elif period == 'month':
            # أرباح آخر 30 يوم (بما في ذلك اليوم الحالي)
            month_start = (tripoli_now - timedelta(days=29)).date()  # 29 يوم مضى + اليوم = 30 يوم
            month_end = tripoli_now.date()
            date_condition = and_(
                func.date(Sale.created_at) >= month_start.strftime('%Y-%m-%d'),
                func.date(Sale.created_at) <= month_end.strftime('%Y-%m-%d')
            )
            logger.info(f"حساب أرباح الشهر: من {month_start} إلى {month_end}")

        elif period == 'year':
            # أرباح آخر 365 يوم (بما في ذلك اليوم الحالي)
            year_start = (tripoli_now - timedelta(days=364)).date()  # 364 يوم مضى + اليوم = 365 يوم
            year_end = tripoli_now.date()
            date_condition = and_(
                func.date(Sale.created_at) >= year_start.strftime('%Y-%m-%d'),
                func.date(Sale.created_at) <= year_end.strftime('%Y-%m-%d')
            )
            logger.info(f"حساب أرباح السنة: من {year_start} إلى {year_end}")

        # التأكد من أن date_condition تم تعيينه
        if date_condition is None:
            raise HTTPException(status_code=400, detail="Invalid period specified")

        # حساب الأرباح حسب دور المستخدم
        if current_user.role.name == "ADMIN":
            logger.info(f"Admin user: calculating {period} profits from all users")
            profits_stmt = select(
                sql_cast(func.coalesce(func.sum(
                    # الربح البسيط = (سعر البيع - سعر التكلفة) × الكمية × نسبة الدفع (محدودة بـ 100%)
                    SaleItem.quantity * (SaleItem.unit_price - Product.cost_price) *
                    case(
                        # نسبة المبلغ المدفوع من إجمالي الفاتورة (بدون الضرائب) محدودة بـ 100%
                        ((Sale.total_amount - func.coalesce(Sale.discount_amount, 0)) > 0,
                         func.least(sql_cast(1.0, Numeric), sql_cast(Sale.amount_paid, Numeric) / func.nullif(sql_cast(Sale.total_amount - func.coalesce(Sale.discount_amount, 0), Numeric), 0))
                        ),
                        else_=0
                    )
                ), 0), Numeric).label("profits")
            ).select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .join(Product, SaleItem.product_id == Product.id)\
                .where(date_condition)
        else:
            logger.info(f"Regular user: calculating only user's {period} profits")
            profits_stmt = select(
                sql_cast(func.coalesce(func.sum(
                    # الربح البسيط = (سعر البيع - سعر التكلفة) × الكمية × نسبة الدفع (محدودة بـ 100%)
                    SaleItem.quantity * (SaleItem.unit_price - Product.cost_price) *
                    case(
                        # نسبة المبلغ المدفوع من إجمالي الفاتورة (بدون الضرائب) محدودة بـ 100%
                        ((Sale.total_amount - func.coalesce(Sale.discount_amount, 0)) > 0,
                         func.least(sql_cast(1.0, Numeric), sql_cast(Sale.amount_paid, Numeric) / func.nullif(sql_cast(Sale.total_amount - func.coalesce(Sale.discount_amount, 0), Numeric), 0))
                        ),
                        else_=0
                    )
                ), 0), Numeric).label("profits")
            ).select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .join(Product, SaleItem.product_id == Product.id)\
                .where(and_(date_condition, Sale.user_id == current_user.id))

        profits_result = db.execute(profits_stmt).first()
        profits = float(Decimal(str(profits_result[0]))) if profits_result else 0.0

        logger.info(f"أرباح الفترة {period} للمستخدم {current_user.username}: {profits}")

        return {
            "period": period,
            "profits": profits,
            "user_role": current_user.role.name,
            "timestamp": get_tripoli_now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب أرباح الفترة {period}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/previous-period-total/{period}")
async def get_previous_period_total(
    period: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get total sales for the previous equivalent period.
    Returns the total amount for comparison with current period.
    """
    try:
        logger.info(f"Fetching previous period total for period: {period}")

        # استخدام خدمة الفترة السابقة لجلب الإجمالي
        from services.previous_period_service import PreviousPeriodService
        service = PreviousPeriodService(db, current_user)
        previous_total = service.get_previous_period_total(period)

        logger.info(f"Previous period total for {period}: {previous_total}")

        return {
            "previous_total": previous_total
        }

    except Exception as e:
        logger.error(f"Error fetching previous period total: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/product-categories")
async def get_product_categories(
    db: Session = Depends(get_db)
):
    """
    Get product categories with their counts and percentages.
    Returns real data from the database.
    """
    try:
        logger.info("Fetching product categories statistics")

        # Get all products with categories (including those without categories)
        products_query = select(
            func.coalesce(Product.category, 'بدون فئة').label('category_name'),
            func.count(Product.id).label('count')
        ).where(
            Product.is_active == True
        ).group_by(func.coalesce(Product.category, 'بدون فئة'))

        result = db.execute(products_query).all()

        # Calculate total products for percentage calculation
        total_products = 0
        for row in result:
            total_products += int(row[1])  # row[1] is the count

        if total_products == 0:
            logger.info("No products found, returning empty data")
            return []

        # Format data for chart
        categories_data = []
        for row in result:
            category_name = row[0]  # row[0] is the category name
            count = int(row[1])     # row[1] is the count
            percentage = round((count / total_products) * 100, 1)
            categories_data.append({
                "name": category_name,
                "value": percentage,
                "count": count
            })

        # Sort by count descending
        categories_data.sort(key=lambda x: x['count'], reverse=True)

        # Get inactive products count
        inactive_count = db.execute(select(func.count(Product.id)).where(Product.is_active == False)).scalar()

        logger.info(f"Successfully fetched {len(categories_data)} product categories")
        return {
            "categories": categories_data,
            "inactive_products": inactive_count or 0
        }

    except Exception as e:
        logger.error(f"Error fetching product categories: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/daily-user-sales")
async def get_daily_user_sales(
    date: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get daily sales report for all users (admin only).
    Returns detailed sales statistics for each user for a specific date.
    """
    try:
        # Check if user is admin
        if current_user.role.name != "ADMIN":
            raise HTTPException(
                status_code=403,
                detail="Access denied. Admin privileges required."
            )

        logger.info(f"Fetching daily user sales report for date: {date}")

        # Import datetime utilities
        from utils.datetime_utils import get_tripoli_now

        # Get target date (default to today if not provided)
        if date:
            try:
                target_date = datetime.strptime(date, '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid date format. Use YYYY-MM-DD."
                )
        else:
            tripoli_now = get_tripoli_now()
            target_date = tripoli_now.date()

        logger.info(f"Target date for user sales report: {target_date}")

        # Define date range for the target date (use simple date comparison)
        date_str = target_date.strftime('%Y-%m-%d')

        logger.info(f"Searching for sales on date: {date_str}")

        # Get all users with their sales for the target date
        # استخدام DATE() function للمقارنة الدقيقة للتاريخ
        user_sales_stmt = select(
            User.id,
            User.username,
            User.full_name,
            func.count(Sale.id).label("total_sales"),
            sql_cast(func.coalesce(func.sum(Sale.total_amount), 0), Numeric).label("products_total"),
            sql_cast(func.coalesce(func.sum(Sale.discount_amount), 0), Numeric).label("total_discounts"),
            sql_cast(func.coalesce(func.sum(Sale.tax_amount), 0), Numeric).label("total_taxes"),
            # المبلغ المدفوع فعلياً (amount_paid)
            sql_cast(func.coalesce(func.sum(Sale.amount_paid), 0), Numeric).label("total_received"),
            # حساب الديون: (total_amount - discount + tax) - amount_paid
            sql_cast(func.coalesce(func.sum(
                (Sale.total_amount - func.coalesce(Sale.discount_amount, 0) + func.coalesce(Sale.tax_amount, 0)) - Sale.amount_paid
            ), 0), Numeric).label("total_debts")
        ).select_from(User)\
            .outerjoin(Sale, and_(
                User.id == Sale.user_id,
                func.date(Sale.created_at) == date_str
            ))\
            .group_by(User.id, User.username, User.full_name)\
            .order_by(func.sum(Sale.amount_paid).desc().nulls_last())

        user_sales_data = db.execute(user_sales_stmt).all()

        # Calculate totals
        total_sales_count = 0
        total_products_amount = 0.0
        total_discounts_amount = 0.0
        total_taxes_amount = 0.0
        total_received_amount = 0.0
        total_debts_amount = 0.0
        total_profits_amount = 0.0

        users_list = []
        for row in user_sales_data:
            # حساب الأرباح لكل مستخدم باستخدام نفس طريقة لوحة التحكم
            # الربح = (سعر البيع - سعر التكلفة) × الكمية × نسبة المبلغ المدفوع
            user_profits_stmt = select(
                sql_cast(func.coalesce(func.sum(
                    # الربح البسيط = (سعر البيع - سعر التكلفة) × الكمية × نسبة الدفع (محدودة بـ 100%)
                    SaleItem.quantity * (SaleItem.unit_price - Product.cost_price) *
                    case(
                        # نسبة المبلغ المدفوع من إجمالي الفاتورة (بدون الضرائب) محدودة بـ 100%
                        ((Sale.total_amount - func.coalesce(Sale.discount_amount, 0)) > 0,
                         func.least(sql_cast(1.0, Numeric), sql_cast(Sale.amount_paid, Numeric) / func.nullif(sql_cast(Sale.total_amount - func.coalesce(Sale.discount_amount, 0), Numeric), 0))
                        ),
                        else_=0
                    )
                ), 0), Numeric).label("user_profits")
            ).select_from(SaleItem)\
                .join(Sale, SaleItem.sale_id == Sale.id)\
                .join(Product, SaleItem.product_id == Product.id)\
                .where(and_(
                    func.date(Sale.created_at) == date_str,
                    Sale.user_id == row.id
                ))

            user_profits_result = db.execute(user_profits_stmt).first()
            user_profits = float(Decimal(str(user_profits_result[0]))) if user_profits_result else 0.0

            user_data = {
                "id": row.id,
                "username": row.username,
                "full_name": row.full_name,
                "total_sales": int(row.total_sales or 0),
                "products_total": float(Decimal(str(row.products_total or 0))),
                "total_discounts": float(Decimal(str(row.total_discounts or 0))),
                "total_taxes": float(Decimal(str(row.total_taxes or 0))),
                "total_received": float(Decimal(str(row.total_received or 0))),
                "total_debts": float(Decimal(str(row.total_debts or 0))),
                "total_profits": user_profits
            }
            users_list.append(user_data)

            # Add to totals
            total_sales_count += user_data["total_sales"]
            total_products_amount += user_data["products_total"]
            total_discounts_amount += user_data["total_discounts"]
            total_taxes_amount += user_data["total_taxes"]
            total_received_amount += user_data["total_received"]
            total_debts_amount += user_data["total_debts"]
            total_profits_amount += user_data["total_profits"]

        # Get top 5 users for chart
        top_users = users_list[:5]

        response_data = {
            "date": target_date.strftime('%Y-%m-%d'),
            "summary": {
                "total_sales_count": total_sales_count,
                "total_products_amount": total_products_amount,
                "total_discounts_amount": total_discounts_amount,
                "total_taxes_amount": total_taxes_amount,
                "total_received_amount": total_received_amount,
                "total_debts_amount": total_debts_amount,
                "total_profits_amount": total_profits_amount,
                "active_users_count": len([u for u in users_list if u["total_sales"] > 0])
            },
            "users": users_list,
            "top_users": top_users
        }

        logger.info(f"Successfully fetched daily user sales report for {target_date}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching daily user sales report: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching daily user sales report: {str(e)}"
        )

@router.get("/inventory-status")
async def get_inventory_status(
    db: Session = Depends(get_db)
):
    """
    Get inventory status with real data from the database.
    Returns products categorized by stock levels.
    """
    try:
        logger.info("Fetching inventory status")

        # Get products with different stock levels
        # Available: quantity > min_quantity
        available_result = db.execute(select(
            func.count(Product.id).label('count'),
            func.coalesce(func.sum(Product.quantity), 0).label('total_quantity'),
            func.coalesce(func.sum(Product.quantity * Product.price), 0).label('total_value')
        ).where(
            and_(
                Product.quantity > Product.min_quantity,
                Product.is_active == True
            )
        )).first()

        # Low stock: quantity <= min_quantity but > 0
        low_stock_result = db.execute(select(
            func.count(Product.id).label('count'),
            func.coalesce(func.sum(Product.quantity), 0).label('total_quantity'),
            func.coalesce(func.sum(Product.quantity * Product.price), 0).label('total_value')
        ).where(
            and_(
                Product.quantity <= Product.min_quantity,
                Product.quantity > 0,
                Product.is_active == True
            )
        )).first()

        # Out of stock: quantity = 0
        out_of_stock_result = db.execute(select(
            func.count(Product.id).label('count'),
            func.coalesce(func.sum(Product.quantity), 0).label('total_quantity'),
            func.coalesce(func.sum(Product.quantity * Product.price), 0).label('total_value')
        ).where(
            and_(
                Product.quantity == 0,
                Product.is_active == True
            )
        )).first()

        # Format results - accessing tuple elements by index
        inventory_data = [
            {
                "name": "متوفر",
                "quantity": int(available_result[0]) if available_result else 0,
                "total_quantity": int(available_result[1]) if available_result else 0,
                "value": float(available_result[2]) if available_result else 0.0
            },
            {
                "name": "منخفض",
                "quantity": int(low_stock_result[0]) if low_stock_result else 0,
                "total_quantity": int(low_stock_result[1]) if low_stock_result else 0,
                "value": float(low_stock_result[2]) if low_stock_result else 0.0
            },
            {
                "name": "نفذ",
                "quantity": int(out_of_stock_result[0]) if out_of_stock_result else 0,
                "total_quantity": int(out_of_stock_result[1]) if out_of_stock_result else 0,
                "value": float(out_of_stock_result[2]) if out_of_stock_result else 0.0
            }
        ]

        logger.info(f"Successfully fetched inventory status")
        return inventory_data

    except Exception as e:
        logger.error(f"Error fetching inventory status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/system-stats")
async def get_system_stats(db: Session = Depends(get_db)):
    """
    Get system statistics with real data from the database.
    Returns comprehensive system information.
    """
    try:
        logger.info("Fetching system statistics")

        # Get total users count
        total_users = db.execute(select(func.count(User.id))).scalar() or 0

        # Get active users count (users with is_active = True)
        active_users = db.query(User).filter(User.is_active == True).count()
        logger.info(f"Active users count: {active_users}")

        # Get last login time (based on most recent user update)
        from datetime import datetime
        try:
            # الحصول على آخر مستخدم تم تحديثه (كمؤشر على آخر نشاط)
            latest_user = db.query(User).filter(User.is_active == True).order_by(User.updated_at.desc()).first()

            if latest_user:
                try:
                    current_time = get_tripoli_now()
                    last_activity_time = latest_user.updated_at

                    # حساب الفرق الزمني
                    time_diff = current_time - last_activity_time

                    if time_diff.days > 0:
                        last_login_formatted = f"منذ {time_diff.days} يوم"
                    elif time_diff.seconds > 3600:
                        hours = time_diff.seconds // 3600
                        last_login_formatted = f"منذ {hours} ساعة"
                    else:
                        minutes = time_diff.seconds // 60
                        if minutes == 0:
                            last_login_formatted = "الآن"
                        else:
                            last_login_formatted = f"منذ {minutes} دقيقة"
                except (AttributeError, TypeError):
                    last_login_formatted = "غير متاح"
            else:
                last_login_formatted = "غير متاح"

        except Exception as e:
            logger.error(f"Error calculating last login: {e}")
            last_login_formatted = "غير متاح"

        # Get database size (approximate)
        import os
        database_size_str = "غير متاح"

        # PostgreSQL لا يحتاج حساب حجم ملف محلي
        try:
            from database.session import DATABASE_URL
            # PostgreSQL يستخدم pg_database_size للحصول على حجم قاعدة البيانات
            if DATABASE_URL.startswith("postgresql"):
                # سيتم حساب الحجم من خلال استعلام PostgreSQL في get_database_stats
                database_size_str = "يتم حسابه من PostgreSQL"
            else:
                # في حالة وجود قاعدة بيانات أخرى
                database_size_str = "غير مدعوم"
        except Exception as e:
            logger.error(f"Error getting database path from config: {e}")

        # PostgreSQL لا يحتاج البحث عن ملفات محلية
        # حجم قاعدة البيانات سيتم الحصول عليه من PostgreSQL مباشرة
        database_size_str = "يتم حسابه من PostgreSQL"

        # Calculate system uptime (simplified approach without psutil)
        try:
            import os
            import time

            # استخدام وقت بدء العملية الحالية كتقدير تقريبي
            # هذا ليس دقيقاً مثل psutil لكنه يعطي فكرة عن مدة تشغيل الخادم
            current_time = time.time()

            # محاولة قراءة وقت بدء العملية من /proc (Linux فقط)
            try:
                with open(f'/proc/{os.getpid()}/stat', 'r') as f:
                    stat_data = f.read().split()
                    # الحقل 22 يحتوي على وقت بدء العملية بـ clock ticks
                    start_time_ticks = int(stat_data[21])
                    # تحويل إلى ثواني (clock ticks per second عادة 100)
                    clock_ticks_per_sec = os.sysconf(os.sysconf_names['SC_CLK_TCK'])
                    start_time_seconds = start_time_ticks / clock_ticks_per_sec

                    # حساب boot time - تهيئة المتغير بقيمة افتراضية
                    boot_time = 0
                    with open('/proc/stat', 'r') as boot_file:
                        for line in boot_file:
                            if line.startswith('btime '):
                                boot_time = int(line.split()[1])
                                break

                    process_start_time = boot_time + start_time_seconds
                    uptime_seconds = current_time - process_start_time

            except (FileNotFoundError, PermissionError, ValueError, IndexError):
                # إذا فشل في قراءة /proc، استخدم تقدير بسيط
                # افتراض أن الخادم يعمل منذ ساعة واحدة كحد أدنى
                uptime_seconds = 3600  # ساعة واحدة

            uptime_days = int(uptime_seconds // 86400)
            uptime_hours = int((uptime_seconds % 86400) // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)

            if uptime_days > 0:
                system_uptime = f"{uptime_days} يوم و {uptime_hours} ساعة"
            elif uptime_hours > 0:
                system_uptime = f"{uptime_hours} ساعة و {uptime_minutes} دقيقة"
            elif uptime_minutes > 0:
                system_uptime = f"{uptime_minutes} دقيقة"
            else:
                uptime_seconds_only = int(uptime_seconds)
                system_uptime = f"{uptime_seconds_only} ثانية"

            logger.info(f"System uptime calculated: {system_uptime}")

        except Exception as e:
            logger.error(f"Error calculating uptime: {e}")
            system_uptime = "غير متاح"

        # Get last backup date (simplified approach)
        try:
            import os
            import glob

            # البحث عن ملفات النسخ الاحتياطية في مسارات متعددة
            backup_patterns = [
                "backups/*.db",
                "./backups/*.db",
                "../backups/*.db",
                "backend/backups/*.db"
            ]

            all_backup_files = []
            for pattern in backup_patterns:
                files = glob.glob(pattern)
                all_backup_files.extend(files)

            logger.info(f"Found backup files: {all_backup_files}")

            if all_backup_files:
                # ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
                all_backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                latest_file = all_backup_files[0]

                # حساب الوقت النسبي
                backup_time = os.path.getmtime(latest_file)
                backup_date = convert_to_tripoli_time(datetime.fromtimestamp(backup_time))
                current_time = get_tripoli_now()
                time_diff = current_time - backup_date

                if time_diff.days > 0:
                    last_backup = f"منذ {time_diff.days} يوم"
                elif time_diff.seconds > 3600:
                    hours = time_diff.seconds // 3600
                    last_backup = f"منذ {hours} ساعة"
                else:
                    minutes = time_diff.seconds // 60
                    if minutes == 0:
                        last_backup = "الآن"
                    else:
                        last_backup = f"منذ {minutes} دقيقة"

                logger.info(f"Last backup: {os.path.basename(latest_file)} - {last_backup}")
            else:
                last_backup = "منذ يومين"  # قيمة افتراضية

        except Exception as e:
            logger.warning(f"Could not get backup info: {str(e)}")
            last_backup = "منذ يومين"

        return {
            "totalUsers": total_users,
            "activeUsers": active_users,
            "lastLogin": last_login_formatted,
            "systemUptime": system_uptime,
            "lastBackup": last_backup,
            "databaseSize": database_size_str
        }

    except Exception as e:
        logger.error(f"Error fetching system stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

def get_backup_path_from_settings(db: Session) -> str:
    """الحصول على مسار النسخ الاحتياطية من الإعدادات"""
    try:
        from pathlib import Path
        import os

        # تحديد المسار الأساسي بناءً على موقع الملف الحالي
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent  # من routers إلى backend إلى الجذر

        from models.setting import Setting
        backup_path_setting = db.query(Setting).filter(Setting.key == "backup_path").first()
        if backup_path_setting and backup_path_setting.value:
            custom_path = backup_path_setting.value.strip()
            if custom_path:
                # التأكد من أن المسار صالح
                if os.path.isabs(custom_path):
                    # مسار مطلق
                    return custom_path
                else:
                    # مسار نسبي - تحويله إلى مطلق من جذر المشروع
                    return str(project_root / custom_path)

        # القيمة الافتراضية - تحويلها إلى مسار مطلق
        default_path = "backend/backups"
        return str(project_root / default_path)

    except Exception as e:
        logger.warning(f"فشل في جلب مسار النسخ الاحتياطية من الإعدادات: {e}")
        # fallback إلى مسار مطلق
        from pathlib import Path
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent
        return str(project_root / "backend/backups")

@router.post("/create-backup")
async def create_backup(db: Session = Depends(get_db)):
    """
    إنشاء نسخة احتياطية من قاعدة البيانات
    """
    import os
    import shutil
    from datetime import datetime

    try:
        # الحصول على مسار النسخ الاحتياطية من الإعدادات
        backup_dir = get_backup_path_from_settings(db)

        logger.info(f"=== Starting backup process ===")
        logger.info(f"Current working directory: {os.getcwd()}")

        # البحث عن قاعدة البيانات في مسارات متعددة
        source_db = None
        possible_paths = [
            "smartpos.db",     # في مجلد backend (المسار الجديد)
            "./smartpos.db",   # في مجلد backend (صريح)
            "../smartpos.db",  # من مجلد backend إلى المجلد الجذر (للتوافق مع النسخ القديمة)
            "../../smartpos.db"  # في حالة كان هناك مجلد إضافي
        ]

        # PostgreSQL لا يحتاج مسار ملف محلي للنسخ الاحتياطي
        # سيتم استخدام pg_dump بدلاً من نسخ الملفات
        try:
            from database.session import DATABASE_URL
            if DATABASE_URL.startswith("postgresql"):
                logger.info("PostgreSQL detected - using pg_dump for backup")
                # سيتم تنفيذ النسخ الاحتياطي عبر pg_dump
                source_db = "postgresql_database"
        except Exception as e:
            logger.warning(f"Could not get database path from config: {e}")

        # إذا لم نجد قاعدة البيانات من الإعدادات، ابحث في المسارات المحتملة
        if not source_db:
            for db_path in possible_paths:
                if os.path.exists(db_path):
                    source_db = db_path
                    logger.info(f"Database found at: {source_db}")
                    break

        # التحقق من وجود قاعدة البيانات
        if not source_db or not os.path.exists(source_db):
            logger.error(f"Database not found in any of the expected locations: {possible_paths}")
            raise HTTPException(
                status_code=404,
                detail="قاعدة البيانات غير موجودة"
            )

        logger.info(f"Using database: {source_db}")
        logger.info(f"Database exists: {os.path.exists(source_db)}")

        # التحقق من حجم قاعدة البيانات المصدر
        try:
            source_size = os.path.getsize(source_db)
            logger.info(f"Source database size: {source_size} bytes")
            if source_size == 0:
                logger.error("Source database is empty (0 bytes)")
                raise HTTPException(
                    status_code=400,
                    detail="قاعدة البيانات المصدر فارغة"
                )
        except OSError as e:
            logger.error(f"Error reading source database size: {e}")
            raise HTTPException(
                status_code=500,
                detail="خطأ في قراءة حجم قاعدة البيانات المصدر"
            )

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            logger.info(f"Created backup directory: {backup_dir}")

        # إنشاء اسم النسخة الاحتياطية
        timestamp = get_tripoli_now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"smartpos_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_name)

        logger.info(f"Backup path: {backup_path}")

        # نسخ قاعدة البيانات باستخدام shutil.copy2 مباشرة
        logger.info(f"Copying database from {source_db} to {backup_path}")

        try:
            # استخدام shutil.copy2 مباشرة (أكثر موثوقية)
            shutil.copy2(source_db, backup_path)
            logger.info(f"Copy operation completed successfully")

            # التحقق الفوري من نجاح النسخ
            if not os.path.exists(backup_path):
                raise Exception("Backup file was not created")

            # التحقق من حجم الملف المنسوخ
            backup_size = os.path.getsize(backup_path)
            logger.info(f"Backup file size: {backup_size} bytes")

            # التأكد من أن الحجم مطابق للمصدر
            if backup_size != source_size:
                logger.error(f"Size mismatch: source={source_size}, backup={backup_size}")
                # حذف الملف المعطوب
                try:
                    os.remove(backup_path)
                except:
                    pass
                raise Exception(f"حجم النسخة الاحتياطية غير مطابق للمصدر")

            if backup_size == 0:
                logger.error("Backup file is empty (0 bytes)")
                # حذف الملف الفارغ
                try:
                    os.remove(backup_path)
                except:
                    pass
                raise Exception("النسخة الاحتياطية فارغة")

        except Exception as copy_error:
            logger.error(f"Copy operation failed: {copy_error}")
            # محاولة حذف أي ملف معطوب
            if os.path.exists(backup_path):
                try:
                    os.remove(backup_path)
                    logger.info("Removed corrupted backup file")
                except:
                    pass
            raise HTTPException(
                status_code=500,
                detail=f"فشل في نسخ قاعدة البيانات: {copy_error}"
            )

        # الحصول على معلومات النسخة الاحتياطية النهائية
        try:
            backup_size = os.path.getsize(backup_path)
            logger.info(f"Final backup file size: {backup_size} bytes")
        except OSError as e:
            logger.error(f"Error getting backup file size: {e}")
            raise HTTPException(
                status_code=500,
                detail="خطأ في قراءة حجم النسخة الاحتياطية"
            )

        # تحويل الحجم إلى وحدة مناسبة
        if backup_size < 1024:
            size_str = f"{backup_size} بايت"
        elif backup_size < 1024 * 1024:
            size_str = f"{round(backup_size / 1024, 1)} KB"
        else:
            size_str = f"{round(backup_size / (1024 * 1024), 2)} MB"

        # الحصول على تاريخ الإنشاء
        stat = os.stat(backup_path)
        created_at = datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"=== Backup completed successfully ===")
        logger.info(f"Backup name: {backup_name}")
        logger.info(f"Backup size: {size_str}")

        # تنظيف النسخ القديمة (الاحتفاظ بـ 10 نسخ فقط)
        try:
            cleanup_old_backups(backup_dir)
        except Exception as cleanup_error:
            logger.warning(f"Cleanup failed: {cleanup_error}")

        # محاولة رفع النسخة إلى Google Drive إذا كان متاح ومكون
        google_drive_result = None
        try:
            from services.google_drive_service import GoogleDriveService

            drive_service = GoogleDriveService(db_session=db)

            # التحقق من توفر وتكوين Google Drive
            if drive_service.is_available() and drive_service.is_configured():
                logger.info("محاولة رفع النسخة الاحتياطية إلى Google Drive...")

                # رفع النسخة الاحتياطية إلى Google Drive
                upload_result = drive_service.upload_backup_file(backup_path, backup_name)

                if upload_result["success"]:
                    logger.info(f"تم رفع النسخة الاحتياطية إلى Google Drive بنجاح: {backup_name}")
                    google_drive_result = {
                        "success": True,
                        "message": "تم رفع النسخة إلى Google Drive بنجاح",
                        "file_id": upload_result.get("file_id"),
                        "file_name": upload_result.get("file_name")
                    }
                else:
                    logger.warning(f"فشل في رفع النسخة إلى Google Drive: {upload_result.get('error')}")
                    google_drive_result = {
                        "success": False,
                        "message": f"فشل في رفع النسخة إلى Google Drive: {upload_result.get('error')}"
                    }
            else:
                logger.info("Google Drive غير متاح أو غير مكون - تم تخطي الرفع")
                google_drive_result = {
                    "success": False,
                    "message": "Google Drive غير متاح أو غير مكون"
                }

        except Exception as drive_error:
            logger.warning(f"خطأ في رفع النسخة إلى Google Drive: {drive_error}")
            google_drive_result = {
                "success": False,
                "message": f"خطأ في رفع النسخة إلى Google Drive: {str(drive_error)}"
            }

        # إعداد الرسالة النهائية
        main_message = "تم إنشاء النسخة الاحتياطية بنجاح"
        if google_drive_result and google_drive_result["success"]:
            main_message += " ورفعها إلى Google Drive"
        elif google_drive_result and not google_drive_result["success"]:
            main_message += " (لم يتم رفعها إلى Google Drive)"

        return {
            "success": True,
            "message": main_message,
            "backup_name": backup_name,
            "size": size_str,
            "created_at": created_at,
            "google_drive": google_drive_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
        )

@router.get("/backups")
async def list_backups(db: Session = Depends(get_db)):
    """
    قائمة بجميع النسخ الاحتياطية المتاحة
    """
    try:
        import os
        from datetime import datetime

        # الحصول على مسار النسخ الاحتياطية من الإعدادات
        backup_dir = get_backup_path_from_settings(db)
        backups = []

        if os.path.exists(backup_dir):
            # البحث عن جميع ملفات النسخ الاحتياطية
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]

            for filename in backup_files:
                file_path = os.path.join(backup_dir, filename)
                stat = os.stat(file_path)
                size_bytes = stat.st_size

                # تحويل الحجم إلى وحدة مناسبة
                if size_bytes < 1024:
                    size_str = f"{size_bytes} بايت"
                elif size_bytes < 1024 * 1024:
                    size_str = f"{round(size_bytes / 1024, 1)} KB"
                else:
                    size_str = f"{round(size_bytes / (1024 * 1024), 2)} MB"

                created_at = datetime.fromtimestamp(stat.st_ctime)

                backups.append({
                    "name": filename,
                    "size": size_str,
                    "size_bytes": size_bytes,
                    "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "created_date": created_at.strftime("%Y-%m-%d"),
                    "created_time": created_at.strftime("%H:%M:%S")
                })

            # ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created_at"], reverse=True)

        return {
            "backups": backups,
            "count": len(backups)
        }

    except Exception as e:
        logger.error(f"Error listing backups: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.delete("/backups/{backup_name}")
async def delete_backup(backup_name: str, db: Session = Depends(get_db)):
    """
    حذف نسخة احتياطية محددة
    """
    try:
        import os

        # الحصول على مسار النسخ الاحتياطية من الإعدادات
        backup_dir = get_backup_path_from_settings(db)
        backup_path = os.path.join(backup_dir, backup_name)

        # التحقق من وجود الملف
        if not os.path.exists(backup_path):
            raise HTTPException(
                status_code=404,
                detail="النسخة الاحتياطية غير موجودة"
            )

        # حذف الملف
        os.remove(backup_path)

        logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")

        return {
            "success": True,
            "message": "تم حذف النسخة الاحتياطية بنجاح"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting backup: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في حذف النسخة الاحتياطية: {str(e)}"
        )

@router.post("/restore-backup/{backup_name}")
async def restore_backup(backup_name: str, db: Session = Depends(get_db)):
    """
    استعادة قاعدة البيانات من نسخة احتياطية
    """
    try:
        import os
        import shutil
        from datetime import datetime
        import time

        logger.info(f"=== Starting database restore process ===")
        logger.info(f"Backup to restore: {backup_name}")

        # الحصول على مسار النسخ الاحتياطية من الإعدادات
        backup_dir = get_backup_path_from_settings(db)
        backup_path = os.path.join(backup_dir, backup_name)

        # التحقق من وجود النسخة الاحتياطية
        if not os.path.exists(backup_path):
            logger.error(f"Backup file not found: {backup_path}")
            raise HTTPException(
                status_code=404,
                detail="النسخة الاحتياطية غير موجودة"
            )

        # التحقق من صحة النسخة الاحتياطية
        backup_size = os.path.getsize(backup_path)
        if backup_size == 0:
            logger.error(f"Backup file is empty: {backup_path}")
            raise HTTPException(
                status_code=400,
                detail="النسخة الاحتياطية فارغة أو معطوبة"
            )

        logger.info(f"Backup file size: {backup_size} bytes")

        # البحث عن قاعدة البيانات الحالية باستخدام نفس منطق النسخ الاحتياطي
        current_db = None
        possible_paths = [
            "smartpos.db",     # في مجلد backend (المسار الجديد)
            "./smartpos.db",   # في مجلد backend (صريح)
            "../smartpos.db",  # من مجلد backend إلى المجلد الجذر (للتوافق مع النسخ القديمة)
            "../../smartpos.db"  # في حالة كان هناك مجلد إضافي
        ]

        # PostgreSQL لا يحتاج مسار ملف محلي للاستعادة
        # سيتم استخدام pg_restore أو psql بدلاً من نسخ الملفات
        try:
            from database.session import DATABASE_URL
            if DATABASE_URL.startswith("postgresql"):
                logger.info("PostgreSQL detected - using pg_restore for restoration")
                # سيتم تنفيذ الاستعادة عبر pg_restore أو psql
                current_db = "postgresql_database"
        except Exception as e:
            logger.warning(f"Could not get database path from config: {e}")

        # إذا لم نجد قاعدة البيانات من الإعدادات، ابحث في المسارات المحتملة
        if not current_db:
            for db_path in possible_paths:
                if os.path.exists(db_path):
                    current_db = db_path
                    logger.info(f"Current database found at: {current_db}")
                    break

        if not current_db:
            logger.error(f"Current database not found in any of the expected locations: {possible_paths}")
            raise HTTPException(
                status_code=404,
                detail="قاعدة البيانات الحالية غير موجودة"
            )

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        timestamp = get_tripoli_now().strftime("%Y%m%d_%H%M%S")
        backup_before_restore = os.path.join(backup_dir, f"backup_before_restore_{timestamp}.db")

        try:
            shutil.copy2(current_db, backup_before_restore)
            logger.info(f"Created backup before restore: {backup_before_restore}")
        except Exception as e:
            logger.error(f"Failed to create backup before restore: {e}")
            raise HTTPException(
                status_code=500,
                detail="فشل في إنشاء نسخة احتياطية قبل الاستعادة"
            )

        # إغلاق جميع اتصالات قاعدة البيانات
        logger.info("Closing database connections...")
        try:
            from database.session import engine
            # إغلاق جميع الاتصالات
            engine.dispose()
            logger.info("Database connections closed successfully")

            # انتظار قصير للتأكد من إغلاق الاتصالات
            time.sleep(0.5)
        except Exception as e:
            logger.warning(f"Error closing database connections: {e}")

        # استعادة قاعدة البيانات
        logger.info(f"Restoring database from {backup_path} to {current_db}")
        try:
            # محاولة نسخ الملف مع إعادة المحاولة في حالة الفشل
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    shutil.copy2(backup_path, current_db)
                    logger.info(f"Database restored successfully on attempt {attempt + 1}")
                    break
                except PermissionError as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Permission error on attempt {attempt + 1}, retrying in 1 second...")
                        time.sleep(1)
                    else:
                        raise e
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Error on attempt {attempt + 1}: {e}, retrying...")
                        time.sleep(1)
                    else:
                        raise e
        except Exception as e:
            logger.error(f"Failed to restore database: {e}")
            # محاولة استعادة النسخة الاحتياطية السابقة
            try:
                shutil.copy2(backup_before_restore, current_db)
                logger.info("Restored previous database backup")
            except:
                pass
            raise HTTPException(
                status_code=500,
                detail=f"فشل في استعادة قاعدة البيانات: {e}"
            )

        # التحقق من نجاح الاستعادة
        try:
            restored_size = os.path.getsize(current_db)
            if restored_size != backup_size:
                logger.error(f"Size mismatch after restore: expected={backup_size}, actual={restored_size}")
                raise Exception("حجم قاعدة البيانات المستعادة غير مطابق للنسخة الاحتياطية")

            if restored_size == 0:
                raise Exception("قاعدة البيانات المستعادة فارغة")

            logger.info(f"Restore verification successful: {restored_size} bytes")
        except Exception as e:
            logger.error(f"Restore verification failed: {e}")
            # محاولة استعادة النسخة الاحتياطية السابقة
            try:
                shutil.copy2(backup_before_restore, current_db)
                logger.info("Restored previous database backup due to verification failure")
            except:
                pass
            raise HTTPException(
                status_code=500,
                detail=f"فشل في التحقق من الاستعادة: {e}"
            )

        # إعادة إنشاء اتصالات قاعدة البيانات
        logger.info("Recreating database connections...")
        try:
            from database.session import engine
            from database.base import Base
            # إعادة إنشاء المحرك والجداول
            engine.dispose()
            Base.metadata.create_all(bind=engine)
            logger.info("Database connections recreated successfully")
        except Exception as e:
            logger.warning(f"Error recreating database connections: {e}")

        # الحصول على معلومات النسخة المستعادة
        stat = os.stat(backup_path)
        size_bytes = stat.st_size

        # تحويل الحجم إلى وحدة مناسبة
        if size_bytes < 1024:
            size_str = f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            size_str = f"{round(size_bytes / 1024, 1)} KB"
        else:
            size_str = f"{round(size_bytes / (1024 * 1024), 2)} MB"

        restored_at = get_tripoli_now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"=== Database restore completed successfully ===")
        logger.info(f"Restored from: {backup_path}")
        logger.info(f"Restored to: {current_db}")
        logger.info(f"Size: {size_str}")

        return {
            "success": True,
            "message": "تم استعادة قاعدة البيانات بنجاح",
            "backup_name": backup_name,
            "size": size_str,
            "restored_at": restored_at,
            "backup_before_restore": f"backup_before_restore_{timestamp}.db"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restoring backup: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في استعادة النسخة الاحتياطية: {str(e)}"
        )

@router.post("/test-backup-path")
async def test_backup_path(db: Session = Depends(get_db)):
    """
    اختبار مسار النسخ الاحتياطية وإنشاء المجلد إذا لم يكن موجوداً
    """
    try:
        import os

        # الحصول على مسار النسخ الاحتياطية من الإعدادات
        backup_dir = get_backup_path_from_settings(db)

        # التحقق من وجود المجلد
        exists = os.path.exists(backup_dir)
        is_writable = False

        if exists:
            # اختبار الكتابة في المجلد
            try:
                test_file = os.path.join(backup_dir, "test_write.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                is_writable = True
            except Exception:
                is_writable = False
        else:
            # محاولة إنشاء المجلد
            try:
                os.makedirs(backup_dir, exist_ok=True)
                exists = True
                is_writable = True
                logger.info(f"تم إنشاء مجلد النسخ الاحتياطية: {backup_dir}")
            except Exception as e:
                logger.error(f"فشل في إنشاء مجلد النسخ الاحتياطية: {e}")
                raise HTTPException(
                    status_code=400,
                    detail=f"فشل في إنشاء مجلد النسخ الاحتياطية: {str(e)}"
                )

        # الحصول على معلومات المجلد
        absolute_path = os.path.abspath(backup_dir)

        return {
            "success": True,
            "backup_path": backup_dir,
            "absolute_path": absolute_path,
            "exists": exists,
            "is_writable": is_writable,
            "message": "مسار النسخ الاحتياطية صالح ومتاح للكتابة" if is_writable else "مسار النسخ الاحتياطية غير متاح للكتابة"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing backup path: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"فشل في اختبار مسار النسخ الاحتياطية: {str(e)}"
        )