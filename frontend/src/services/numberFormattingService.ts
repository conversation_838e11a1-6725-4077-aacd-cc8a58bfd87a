/**
 * خدمة تنسيق الأرقام المالية
 * تتعامل مع تنسيق الأرقام المالية بطرق مختلفة مع دعم الفواصل والتنسيقات المتنوعة
 * تطبق مبادئ البرمجة الكائنية (OOP) مع نمط Singleton
 */

import api from '../lib/axios';

// أنواع التنسيقات المدعومة
export type NumberFormatType = 'thousands' | 'millions' | 'billions' | 'trillions' | 'quadrillions';

// واجهة إعدادات التنسيق
export interface NumberFormatSettings {
  separatorType: 'comma' | 'space' | 'none';
  formatType: NumberFormatType;
  showDecimals: boolean;
  decimalPlaces: number;
  currencySymbol: string;
  symbolPosition: 'before' | 'after';
}

// واجهة تفاصيل التنسيق
export interface FormatDetails {
  id: NumberFormatType;
  name: string;
  separator: string;
  example: string;
  order: number;
}

/**
 * خدمة تنسيق الأرقام المالية
 * تطبق نمط Singleton لضمان وجود نسخة واحدة فقط
 */
export class NumberFormattingService {
  private static instance: NumberFormattingService;
  private settings: NumberFormatSettings | null = null;
  private lastFetchTime = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 دقائق - زيادة مدة الكاش
  private fetchPromise: Promise<NumberFormatSettings> | null = null; // منع الطلبات المتزامنة

  // الإعدادات الافتراضية
  private readonly DEFAULT_SETTINGS: NumberFormatSettings = {
    separatorType: 'comma',
    formatType: 'thousands',
    showDecimals: true,
    decimalPlaces: 2,
    currencySymbol: 'د.ل',
    symbolPosition: 'after'
  };

  // تفاصيل التنسيقات المدعومة
  private readonly FORMAT_DETAILS: FormatDetails[] = [
    {
      id: 'thousands',
      name: 'فاصل الآلاف',
      separator: '1,000',
      example: '1,000',
      order: 1
    },
    {
      id: 'millions',
      name: 'فاصل الملايين',
      separator: '1,000,000',
      example: '1,000,000',
      order: 2
    },
    {
      id: 'billions',
      name: 'فاصل المليارات',
      separator: '1,000,000,000',
      example: '1,000,000,000',
      order: 3
    },
    {
      id: 'trillions',
      name: 'فاصل التريليونات',
      separator: '1,000,000,000,000',
      example: '1,000,000,000,000',
      order: 4
    },
    {
      id: 'quadrillions',
      name: 'فاصل الكوادريليونات',
      separator: '1,000,000,000,000,000',
      example: '1,000,000,000,000,000',
      order: 5
    }
  ];

  private constructor() {
    // منع إنشاء نسخ متعددة
  }

  /**
   * الحصول على النسخة الوحيدة من الخدمة
   */
  public static getInstance(): NumberFormattingService {
    if (!NumberFormattingService.instance) {
      NumberFormattingService.instance = new NumberFormattingService();
    }
    return NumberFormattingService.instance;
  }

  /**
   * جلب إعدادات التنسيق من الخادم
   */
  private async fetchSettings(): Promise<NumberFormatSettings> {
    const now = Date.now();

    // استخدام الكاش إذا كان متاحاً وحديثاً
    if (this.settings && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.settings;
    }

    // منع الطلبات المتزامنة - إذا كان هناك طلب جاري، انتظره
    if (this.fetchPromise) {
      return this.fetchPromise;
    }

    // إنشاء طلب جديد
    this.fetchPromise = this.performFetch();

    try {
      const result = await this.fetchPromise;
      return result;
    } finally {
      // مسح الطلب بعد الانتهاء
      this.fetchPromise = null;
    }
  }

  /**
   * تنفيذ الطلب الفعلي للخادم
   */
  private async performFetch(): Promise<NumberFormatSettings> {
    try {
      const response = await api.get('/api/settings/public');
      const serverSettings = response.data;

      const settings: NumberFormatSettings = {
        separatorType: serverSettings.number_separator_type || this.DEFAULT_SETTINGS.separatorType,
        formatType: serverSettings.number_format_type || this.DEFAULT_SETTINGS.formatType,
        showDecimals: serverSettings.show_decimals !== undefined ?
          (typeof serverSettings.show_decimals === 'boolean' ?
            serverSettings.show_decimals :
            serverSettings.show_decimals === 'true') : this.DEFAULT_SETTINGS.showDecimals,
        decimalPlaces: parseInt(serverSettings.decimal_places) || this.DEFAULT_SETTINGS.decimalPlaces,
        currencySymbol: serverSettings.currency_symbol || this.DEFAULT_SETTINGS.currencySymbol,
        symbolPosition: serverSettings.currency_position || this.DEFAULT_SETTINGS.symbolPosition
      };

      // تحديث الكاش
      this.settings = settings;
      this.lastFetchTime = Date.now();

      return settings;
    } catch (error) {
      console.warn('فشل في جلب إعدادات تنسيق الأرقام، استخدام الإعدادات الافتراضية:', error);

      // حفظ الإعدادات الافتراضية في الكاش لتجنب طلبات متكررة
      this.settings = this.DEFAULT_SETTINGS;
      this.lastFetchTime = Date.now();

      return this.DEFAULT_SETTINGS;
    }
  }

  /**
   * تنسيق رقم مالي حسب الإعدادات
   */
  public async formatNumber(amount: number): Promise<string> {
    const settings = await this.fetchSettings();
    
    if (isNaN(amount) || !isFinite(amount)) {
      return '0';
    }

    // تطبيق عدد الأرقام العشرية
    const fixedAmount = settings.showDecimals ? 
      amount.toFixed(settings.decimalPlaces) : 
      Math.round(amount).toString();

    // تطبيق الفواصل
    const formattedNumber = this.applySeparators(fixedAmount, settings.separatorType);

    return formattedNumber;
  }

  /**
   * تنسيق رقم مالي مع رمز العملة
   */
  public async formatCurrency(amount: number): Promise<string> {
    const settings = await this.fetchSettings();
    const formattedNumber = await this.formatNumber(amount);

    if (settings.symbolPosition === 'before') {
      return `${settings.currencySymbol} ${formattedNumber}`;
    } else {
      return `${formattedNumber} ${settings.currencySymbol}`;
    }
  }

  /**
   * تطبيق الفواصل على الرقم
   */
  private applySeparators(numberStr: string, separatorType: 'comma' | 'space' | 'none'): string {
    if (separatorType === 'none') {
      return numberStr;
    }

    const parts = numberStr.split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];

    const separator = separatorType === 'comma' ? ',' : ' ';
    
    // تطبيق الفاصل على الجزء الصحيح
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);

    // إرجاع الرقم مع الجزء العشري إن وجد
    return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
  }

  /**
   * الحصول على تفاصيل التنسيقات المدعومة
   */
  public getFormatDetails(): FormatDetails[] {
    return [...this.FORMAT_DETAILS];
  }

  /**
   * الحصول على مثال منسق لرقم معين
   */
  public async getFormattedExample(amount: number): Promise<string> {
    return await this.formatCurrency(amount);
  }

  /**
   * تحديث إعدادات التنسيق
   */
  public async updateSettings(newSettings: Partial<NumberFormatSettings>): Promise<void> {
    try {
      // إرسال الإعدادات الجديدة للخادم
      const updatePromises = Object.entries(newSettings).map(([key, value]) => {
        const serverKey = this.mapClientKeyToServerKey(key);
        return api.put(`/api/settings/${serverKey}`, { value: value.toString() });
      });

      await Promise.all(updatePromises);

      // مسح الكاش لإجبار إعادة التحميل
      this.clearCache();
    } catch (error) {
      console.error('فشل في تحديث إعدادات تنسيق الأرقام:', error);
      throw error;
    }
  }

  /**
   * تحويل مفاتيح العميل إلى مفاتيح الخادم
   */
  private mapClientKeyToServerKey(clientKey: string): string {
    const keyMap: Record<string, string> = {
      'separatorType': 'number_separator_type',
      'formatType': 'number_format_type',
      'showDecimals': 'show_decimals',
      'decimalPlaces': 'decimal_places',
      'currencySymbol': 'currency_symbol',
      'symbolPosition': 'currency_position'
    };

    return keyMap[clientKey] || clientKey;
  }

  /**
   * مسح الكاش
   */
  public clearCache(): void {
    this.settings = null;
    this.lastFetchTime = 0;
  }

  /**
   * الحصول على الإعدادات الحالية
   */
  public async getCurrentSettings(): Promise<NumberFormatSettings> {
    return await this.fetchSettings();
  }
}

// تصدير النسخة الوحيدة من الخدمة
export const numberFormattingService = NumberFormattingService.getInstance();
